import React from "react";
import type { ConversationMessage } from "../utils/conversationUtils";

interface MessageBubbleProps {
  message: ConversationMessage;
}

/**
 * Message bubble component for displaying conversation messages
 *
 * Features:
 * - Different styling for user vs AI messages
 * - Support for interim (temporary) messages
 * - Timestamp display
 * - Smooth animations
 */
export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.type === "user";

  console.log("🔍 [MessageBubble] Renderizando mensaje:", message.id, message.type, message.content.substring(0, 30));

  return (
    <div
      style={{
        display: "flex",
        justifyContent: isUser ? "flex-end" : "flex-start",
        marginBottom: "16px",
        animation: "slideIn 0.3s ease-out",
      }}
    >
      <div
        style={{
          // maxWidth: "85%",
          padding: "40px 20px",
          borderRadius: "12px",
          border: "1px solid #88FFD5",
          backgroundColor: "#002332",
          color: "#88FFD5",
          fontSize: "24px",
          fontWeight: "400",
          lineHeight: "150%",
          position: "relative",
        }}
      >
        {/* Message content */}
        <div style={{ marginBottom: message.isInterim ? "8px" : "4px" }}>
          {message.content}
        </div>

        {/* Interim message indicator */}
        {message.isInterim && (
          <div
            style={{
              fontSize: "11px",
              opacity: 0.8,
              fontStyle: "italic",
              color: isUser ? "#e3f2fd" : "#666",
            }}
          >
            Escribiendo...
          </div>
        )}
      </div>
    </div>
  );
};
