import React from "react";
import { Mic } from "microapps";

type ConversationState =
  | "idle"
  | "listening"
  | "processing"
  | "speaking"
  | "error"
  | "reset";

interface MicAdapterProps {
  conversationState: ConversationState;
  isVoiceActive: boolean;     // tu flag actual
  micLevel: number;           // 0..1
  onToggle: () => void;       // tu toggleVoice
  voiceError?: string | null; // tu error si existe
  className?: string;
  id?: string;
}

function mapToMicState(
  st: ConversationState,
  isVoiceActive: boolean,
  hasError: boolean,
  errorMessage?: string | null
): "default" | "recording" | "disabled" | "reset" {
  // Solo deshabilitar completamente para errores críticos (no para "no-speech")
  if (hasError && errorMessage && !errorMessage.includes("No se detectó habla")) {
    return "disabled";
  }

  switch (st) {
    case "reset":
      return "reset";
    case "listening":
      // grabando / escuchando → animación del anillo de Mic
      return "recording";
    case "processing":
    case "speaking":
      // durante thinking/tts bloqueamos el botón
      return "disabled";
    case "idle":
    default:
      return isVoiceActive ? "recording" : "default";
  }
}

export const MicAdapter: React.FC<MicAdapterProps> = ({
  conversationState,
  isVoiceActive,
  micLevel,
  onToggle,
  voiceError,
  className,
  id,
}) => {
  const state = mapToMicState(
    conversationState,
    isVoiceActive,
    !!voiceError,
    voiceError
  );

  const level = Math.max(0, Math.min(100, Math.round((micLevel ?? 0) * 100)));

  return (
    <div className={className}>
      <Mic
        id={id}
        className="game-mic"
        level={level}
        onClick={onToggle}
        state={state}
      />
      {!!voiceError && (
        <div className="voice-error" style={{ color: "red", fontSize: 12, marginTop: 4 }}>
          {voiceError}
        </div>
      )}
    </div>
  );
};
