// src/services/ConversationStorage.ts

export interface StoredMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: number;
}

export interface GameHint {
  id: string;
  content: string;
  createdAt: number;
  questionNumber: number;
}

export interface GameProgress {
  questionsAsked: number;
  questionsRemaining: number;
  hints: GameHint[];
  gameFinished: boolean;
  gameWon: boolean;
}

export interface ConversationSession {
  id: string;
  title: string;
  version: number;
  createdAt: number;
  updatedAt: number;
  generatedCharacter?: string;
  messages: StoredMessage[];
  gameProgress?: GameProgress;
}

export class ConversationStorage {
  private static instance: ConversationStorage;
  private currentSession: ConversationSession | null = null;
  private readonly storageKey = 'genigma-conversations';

  private constructor() {}

  static getInstance(): ConversationStorage {
    if (!ConversationStorage.instance) {
      ConversationStorage.instance = new ConversationStorage();
    }
    return ConversationStorage.instance;
  }

  // Crear nueva sesión de conversación
  createNewSession(generatedCharacter?: string): ConversationSession {
    const now = Date.now();
    const sessionId = `guess-who-${now}`;

    this.currentSession = {
      id: sessionId,
      title: "Adivina la persona",
      version: 1,
      createdAt: now,
      updatedAt: now,
      generatedCharacter,
      messages: [],
      gameProgress: {
        questionsAsked: 0,
        questionsRemaining: 20,
        hints: [],
        gameFinished: false,
        gameWon: false
      }
    };

    this.saveToStorage();
    // console.log('📝 Nueva sesión creada:', this.currentSession.id);
    return this.currentSession;
  }

  // Obtener sesión actual
  getCurrentSession(): ConversationSession | null {
    return this.currentSession;
  }

  // Cargar sesión desde localStorage (la más reciente)
  loadLatestSession(): ConversationSession | null {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return null;

      const sessions: ConversationSession[] = JSON.parse(stored);
      if (sessions.length === 0) return null;

      // Obtener la sesión más reciente
      const latest = sessions.reduce((prev, current) =>
        prev.updatedAt > current.updatedAt ? prev : current
      );

      this.currentSession = latest;
      // console.log('📖 Sesión cargada:', latest.id, 'con', latest.messages.length, 'mensajes');
      return latest;
    } catch (error) {
      console.error('❌ Error cargando sesión:', error);
      return null;
    }
  }

  // Agregar mensaje a la sesión actual
  addMessage(role: 'user' | 'assistant', content: string): StoredMessage {
    if (!this.currentSession) {
      throw new Error('No hay sesión activa');
    }

    const now = Date.now();
    const messageId = role === 'user'
      ? `u-${this.currentSession.messages.filter(m => m.role === 'user').length + 1}`
      : `a-${this.currentSession.messages.filter(m => m.role === 'assistant').length + 1}`;

    const newMessage: StoredMessage = {
      id: messageId,
      role,
      content,
      createdAt: now
    };

    this.currentSession.messages.push(newMessage);
    this.currentSession.updatedAt = now;

    this.saveToStorage();
    console.log('💬 Mensaje agregado:', messageId, '-', content.substring(0, 50) + '...');
    return newMessage;
  }

  // Actualizar progreso del juego
  updateGameProgress(questionsRemaining?: number, hint?: string, gameFinished?: boolean, gameWon?: boolean): void {
    if (!this.currentSession) {
      throw new Error('No hay sesión activa');
    }

    if (!this.currentSession.gameProgress) {
      this.currentSession.gameProgress = {
        questionsAsked: 0,
        questionsRemaining: 20,
        hints: [],
        gameFinished: false,
        gameWon: false
      };
    }

    const progress = this.currentSession.gameProgress;

    if (questionsRemaining !== undefined) {
      progress.questionsAsked = 20 - questionsRemaining;
      progress.questionsRemaining = questionsRemaining;
    }

    if (hint) {
      const newHint: GameHint = {
        id: `hint-${progress.hints.length + 1}`,
        content: hint,
        createdAt: Date.now(),
        questionNumber: progress.questionsAsked
      };
      progress.hints.push(newHint);
    }

    if (gameFinished !== undefined) {
      progress.gameFinished = gameFinished;
    }

    if (gameWon !== undefined) {
      progress.gameWon = gameWon;
    }

    this.currentSession.updatedAt = Date.now();
    this.saveToStorage();
    console.log('🎮 Progreso del juego actualizado:', progress);
  }

  // Obtener progreso del juego actual
  getGameProgress(): GameProgress | null {
    return this.currentSession?.gameProgress || null;
  }

  // Limpiar pistas del juego actual
  clearGameHints(): void {
    if (this.currentSession?.gameProgress) {
      this.currentSession.gameProgress.hints = [];
      this.currentSession.updatedAt = Date.now();
      this.saveToStorage();
      console.log('🧹 Pistas del juego limpiadas');
    }
  }

  // Reiniciar progreso del juego completamente
  resetGameProgress(): void {
    if (this.currentSession) {
      this.currentSession.gameProgress = {
        questionsAsked: 0,
        questionsRemaining: 20,
        hints: [],
        gameFinished: false,
        gameWon: false
      };
      this.currentSession.updatedAt = Date.now();
      this.saveToStorage();
      console.log('🔄 Progreso del juego reiniciado');
    }
  }

  // Agregar mensaje inicial del asistente (seed)
  addSeedMessage(content: string): StoredMessage {
    if (!this.currentSession) {
      throw new Error('No hay sesión activa');
    }

    const seedMessage: StoredMessage = {
      id: 'seed-1',
      role: 'assistant',
      content,
      createdAt: this.currentSession.createdAt
    };

    // Agregar al inicio si no existe ya
    const existingSeed = this.currentSession.messages.find(m => m.id === 'seed-1');
    if (!existingSeed) {
      this.currentSession.messages.unshift(seedMessage);
      this.currentSession.updatedAt = Date.now();
      this.saveToStorage();
      // console.log('🌱 Mensaje seed agregado:', content.substring(0, 50) + '...');
    }

    return seedMessage;
  }

  // Obtener todos los mensajes de la sesión actual
  getMessages(): StoredMessage[] {
    return this.currentSession?.messages || [];
  }

  // Limpiar sesión actual
  clearCurrentSession(): void {
    this.currentSession = null;
    console.log('🗑️ Sesión actual limpiada');
  }

  // Limpiar completamente todas las conversaciones del localStorage
  clearAllConversations(): void {
    try {
      localStorage.removeItem(this.storageKey);
      this.currentSession = null;
      // console.log('🗑️ Todas las conversaciones eliminadas del localStorage');
    } catch (error) {
      console.error('❌ Error eliminando conversaciones del localStorage:', error);
    }
  }

  // Guardar en localStorage
  private saveToStorage(): void {
    if (!this.currentSession) return;

    try {
      const existing = localStorage.getItem(this.storageKey);
      let sessions: ConversationSession[] = existing ? JSON.parse(existing) : [];

      // Buscar si ya existe esta sesión y actualizarla, o agregarla
      const existingIndex = sessions.findIndex(s => s.id === this.currentSession!.id);
      if (existingIndex >= 0) {
        sessions[existingIndex] = this.currentSession;
      } else {
        sessions.push(this.currentSession);
      }

      // Mantener solo las últimas 10 sesiones para no llenar localStorage
      sessions = sessions
        .sort((a, b) => b.updatedAt - a.updatedAt)
        .slice(0, 10);

      localStorage.setItem(this.storageKey, JSON.stringify(sessions));
      // console.log('💾 Sesión guardada en localStorage');
    } catch (error) {
      console.error('❌ Error guardando en localStorage:', error);
    }
  }

  // Exportar sesión para debug
  exportSession(): string {
    return JSON.stringify(this.currentSession, null, 2);
  }
}
